# iOS界面线框图与导航流程

> **文档版本**：v1.1
> **最后更新**：2024年12月
> **相关文档**：[iOS_AI助手应用设计规范.md](./iOS_AI助手应用设计规范.md) | [界面布局表格描述.md](./界面布局表格描述.md)

## 应用架构概览

### 信息架构 (优化版)

```
YourAI Agent App
├── 启动页 (Launch Screen)
├── 用户认证流程
│   ├── 登录页面
│   ├── 注册页面
│   └── 忘记密码页面
└── 主应用界面
    ├── 聊天界面 (主界面)
    │   ├── 智能体下拉选择 (内嵌)
    │   └── 智能体切换功能
    ├── 历史会话记录界面 (新增)
    ├── 个人资料界面 (优化)
    └── 设置界面
```

## 详细线框图

### 1. 启动页 (Launch Screen)

```
┌──────────────────────────────────────┐
│                                      │
│             Safe Area                │
│                                      │
│                                      │
│           ┌─────────────┐            │
│           │             │            │
│           │   App Icon  │            │
│           │             │            │
│           └─────────────┘            │
│                                      │
│           YourAI Agent               │
│                                      │
│             Safe Area                │
│                                      │
└──────────────────────────────────────┘
```

**元素说明：**

- App图标：120pt × 120pt，居中显示
- 应用名称：标题3字体，图标下方24pt
- 背景：纯白色
- 无其他装饰元素

### 2. 登录界面

```
┌──────────────────────────────────────┐
│  ←                        Settings   │
├──────────────────────────────────────┤
│                                      │
│            Welcome Back              │
│       Start Your AI Conversation     │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ @ Email Address                │  │
│  └────────────────────────────────┘  │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ * Password                     │  │
│  └────────────────────────────────┘  │
│                     Forgot Password  │
│                                      │
│  ┌────────────────────────────────┐  │
│  │            Login               │  │
│  └────────────────────────────────┘  │
│                                      │
│             ──── OR ────             │
│                                      │
│  ┌────────────────────────────────┐  │
│  │   Sign in with Apple ID        │  │
│  └────────────────────────────────┘  │
│                                      │
│  ┌────────────────────────────────┐  │
│  │    Sign in with Google         │  │
│  └────────────────────────────────┘  │
│                                      │
│        Don't have account? Sign Up   │
│                                      │
└──────────────────────────────────────┘
```

### 3. 注册界面 (第一步)

```
┌──────────────────────────────────────┐
│  ←      Sign Up        [1][2][3]     │
├──────────────────────────────────────┤
│                                      │
│           Create Account             │
│      Please enter your information   │
│                                      │
│  ┌────────────────────────────────┐  │
│  │    Username                    │  │
│  └────────────────────────────────┘  │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ @ Email Address                │  │
│  └────────────────────────────────┘  │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ * Set Password                 │  │
│  └────────────────────────────────┘  │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ * Confirm Password             │  │
│  └────────────────────────────────┘  │
│                                      │
│  ┌────────────────────────────────┐  │
│  │             Next               │  │
│  └────────────────────────────────┘  │
│                                      │
│       Already have account? Login    │
│                                      │
└──────────────────────────────────────┘
```

### 4. 主聊天界面 (优化版)

```
┌──────────────────────────────────────┐
│  [H]   Finance Agent v      [U]      │
├──────────────────────────────────────┤
│                                      │
│  [── Today ──]                       │
│                                      │
│                   ┌─────────────┐    │
│                   │ Hello, help │    │
│                   │ me analyze  │    │
│                   │ today       │    │
│                   └─────────────┘    │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ Hello! I'll help you analyze   │  │
│  │ today's market. Let me query   │  │
│  │ the latest data...             │  │
│  │                                │  │
│  │ [Analyzing...] Loading         │  │
│  └────────────────────────────────┘  │
│                                      │
│                   ┌─────────────┐    │
│                   │ Thank you!  │    │
│                   └─────────────┘    │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ Based on latest analysis:      │  │
│  │ • Index A: +1.2%               │  │
│  │ • Index B: +0.8%               │  │
│  │ • Index C: +2.1%               │  │
│  │                                │  │
│  │ Overall market positive...     │  │
│  └────────────────────────────────┘  │
│                                      │
├──────────────────────────────────────┤
│  [+]  Type message...            [M] │
│                                      │
└──────────────────────────────────────┘
```

**界面优化说明：**

- 左侧：[H] 历史会话按钮 (History)，替代原来的菜单按钮
- 中央：智能体名称 + 下拉箭头 (v)，可点击切换智能体
- 右侧：[U] 个人头像按钮 (User)，替代原来的设置按钮
- 输入区域：[+] 附件按钮，[M] 语音/发送按钮 (Mic/Message)

### 5. 历史会话记录界面

```
┌──────────────────────────────────────┐
│  <        Chat History          [S]  │
├──────────────────────────────────────┤
│                                      │
│  [── Today ──]                       │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ [$] Market Analysis       [.]  │  │
│  │     How is today's trend?      │  │
│  │     2 min ago • 15 messages    │  │
│  └────────────────────────────────┘  │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ [?] Search Questions      [.]  │  │
│  │     Find latest AI news        │  │
│  │     1 hour ago • 8 messages    │  │
│  └────────────────────────────────┘  │
│                                      │
│  [── Yesterday ──]                   │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ [C] Code Assistant        [.]  │  │
│  │     How to optimize React?     │  │
│  │     Yesterday • 23 messages    │  │
│  └────────────────────────────────┘  │
│                                      │
│  ┌────────────────────────────────┐  │
│  │ [B] Crypto Prediction     [.]  │  │
│  │     Bitcoin price analysis     │  │
│  │     Yesterday • 12 messages    │  │
│  └────────────────────────────────┘  │
│                                      │
└──────────────────────────────────────┘
```

**界面说明：**

- 按时间分组显示历史会话 (Today, Yesterday)
- 智能体图标：[$] Finance, [?] Search, [C] Code, [B] Bitcoin
- 每个会话显示标题、最后消息预览、时间和消息数
- 右侧 [.] 表示更多操作按钮
- 顶部 [S] 表示搜索按钮
- 使用英文内容确保ASCII字符对齐

### 6. 智能体下拉选择弹窗

```
┌──────────────────────────────────────┐
│  [H]   Finance Agent ^      [U]      │
├──────────────────────────────────────┤
│ ████████████████████████████████████ │ ← 半透明遮罩
│ ████████████████████████████████████ │
│ ┌──────────────────────────────────┐ │
│ │              ────                │ │ ← 顶部指示器
│ │                                  │ │
│ │  ┌────────────────────────────┐  │ │
│ │  │ [?] Web Search Agent   [v] │  │ │
│ │  │     Web search and info    │  │ │
│ │  └────────────────────────────┘  │ │
│ │                                  │ │
│ │  ┌────────────────────────────┐  │ │
│ │  │ [$] Finance Agent          │  │ │
│ │  │     Financial analysis     │  │ │
│ │  └────────────────────────────┘  │ │
│ │                                  │ │
│ │  ┌────────────────────────────┐  │ │
│ │  │ [A] Agno Assist            │  │ │
│ │  │     Framework guidance     │  │ │
│ │  └────────────────────────────┘  │ │
│ │                                  │ │
│ │  ┌────────────────────────────┐  │ │
│ │  │ [B] Crypto Prediction      │  │ │
│ │  │     Market analysis        │  │ │
│ │  └────────────────────────────┘  │ │
│ │                                  │ │
│ └──────────────────────────────────┘ │
│ ████████████████████████████████████ │
│ ████████████████████████████████████ │
└──────────────────────────────────────┘
```

**交互说明：**

- 点击主界面中央的智能体名称触发
- 从顶部滑下展开，带有平滑动画
- 点击选项或背景遮罩收起
- 选中项用 [v] 表示勾选标识
- 智能体图标：[?] Search, [$] Finance, [A] Agno, [B] Bitcoin

### 7. 设置界面

```
┌──────────────────────────────────────┐
│  ←           Settings                │
├──────────────────────────────────────┤
│                                      │
│  ┌── Account ──────────────────────┐ │
│  │                                 │ │
│  │     Profile                  >  │ │
│  │      Account Security        >  │ │
│  │                                 │ │
│  └─────────────────────────────────┘ │
│                                      │
│  ┌── App Settings ─────────────────┐ │
│  │                                 │ │
│  │      Dark Mode               ○  │ │
│  │      Notifications           >  │ │
│  │      Voice Settings          >  │ │
│  │      Language Settings       >  │ │
│  │                                 │ │
│  └─────────────────────────────────┘ │
│                                      │
│  ┌── Data & Privacy ───────────────┐ │
│  │                                 │ │
│  │      Data Usage              >  │ │
│  │      Privacy Settings        >  │ │
│  │      Clear Chat History      >  │ │
│  │                                 │ │
│  └─────────────────────────────────┘ │
│                                      │
│  ┌── Help & Support ───────────────┐ │
│  │                                 │ │
│  │      Help Center             >  │ │
│  │      Contact Us              >  │ │
│  │      Rate App                >  │ │
│  │                                 │ │
│  └─────────────────────────────────┘ │
│                                      │
│              Sign Out                │
│                                      │
└──────────────────────────────────────┘
```

## 导航流程图

### 多种界面描述格式

为了解决ASCII线框图的对齐问题，我们提供了三种不同的界面描述格式：

1. **Mermaid交互式图表** - 专业的可视化界面布局图，支持缩放和交互
2. **表格格式描述** - 详见 `界面布局表格描述.md`，结构化的界面元素说明
3. **简化文本线框图** - 以下提供的兼容性最好的文本版本

### 简化版界面结构

#### 登录界面结构

```
Navigation: [Back] Settings
Title: Welcome Back
Subtitle: Start Your AI Conversation

Inputs:
  - Email Address Field
  - Password Field
  - Forgot Password Link

Actions:
  - Login Button (Primary)
  - Divider: ---- OR ----
  - Apple ID Login Button
  - Google Login Button
  - Sign Up Link

Layout: Vertical stack with 16pt spacing
```

#### 主聊天界面结构 (优化版)

```
Navigation: [History] Agent Selector v [User]

Chat Area:
  - Date Separator: [-- Today --]
  - User Messages: Right-aligned blue bubbles
  - AI Messages: Left-aligned gray bubbles
  - Typing Indicator: [Analyzing...] Loading

Input Area:
  - [Attach] Text Input Field [Voice/Send]

Layout: Full screen with input at bottom
```

#### 历史会话记录界面结构

```
Navigation: [Back] Chat History [Search]

Search Bar: Search Input Field + Cancel Button

Chat Sessions:
  Time Groups: Today, Yesterday, This Week, etc.

  Session Cards:
  1. Market Analysis Discussion
     Agent: Finance Agent
     Preview: How is today's market trend?
     Time: 2 minutes ago • 15 messages

  2. Search Related Questions
     Agent: Web Search Agent
     Preview: Help me find latest AI news
     Time: 1 hour ago • 8 messages

Actions: Long press for context menu
- Resume Session
- Rename Session
- Pin Session
- Share Session
- Delete Session

Layout: Vertical list grouped by time
```

### 主要用户流程 (优化版)

```mermaid
graph TD
    A[启动页] --> B{用户状态}
    B -->|已登录| C[主聊天界面]
    B -->|未登录| D[登录界面]

    D --> E[注册界面]
    D --> F[忘记密码]
    D --> C
    E --> C
    F --> D

    C --> G[历史会话记录界面]
    C --> H[个人资料界面]
    C --> I[智能体下拉选择]

    G --> C
    G --> J[恢复历史会话]
    J --> C

    H --> K[设置界面]
    H --> L[退出登录]
    L --> D

    K --> M[各种设置子页面]
    M --> K
    K --> H

    I --> N[选择智能体]
    N --> C
```

### 详细交互流程

#### 1. 用户认证流程

```
启动应用
    ↓
检查登录状态
    ├─ 已登录 → 进入主界面
    └─ 未登录 → 显示登录界面
        ├─ 输入凭据 → 验证 → 成功 → 主界面
        ├─ 注册新账户 → 注册流程 → 主界面
        └─ 忘记密码 → 重置流程 → 登录界面
```

#### 2. 主要对话流程 (优化版)

```
主聊天界面
    ↓
点击智能体下拉选择 (可选)
    ↓
选择智能体 → 更新界面显示
    ↓
输入消息
    ↓
发送消息 → 显示发送状态
    ↓
接收AI回复 → 显示打字效果
    ↓
继续对话 或 结束会话
```

#### 3. 历史会话管理流程 (新增)

```
主聊天界面 → 点击历史会话按钮
    ↓
历史会话记录界面
    ├─ 搜索会话 → 筛选结果
    ├─ 点击会话 → 恢复对话
    ├─ 长按会话 → 操作菜单
    │   ├─ 重命名会话
    │   ├─ 置顶会话
    │   ├─ 分享会话
    │   └─ 删除会话
    └─ 返回主界面
```

#### 4. 个人资料与设置流程 (优化版)

```
主界面 → 点击个人头像
    ↓
个人资料界面
    ├─ 编辑个人信息 → 保存
    ├─ 查看使用统计
    ├─ 进入设置界面
    │   ├─ 应用设置 → 修改 → 自动保存
    │   ├─ 数据隐私 → 查看/清除
    │   ├─ 帮助支持 → 外部链接/页面
    │   └─ 退出登录
    └─ 返回主界面
```

## 状态管理

### 应用状态

- **启动状态**：冷启动、热启动、后台恢复
- **认证状态**：未登录、登录中、已登录、登录失败
- **网络状态**：在线、离线、连接中、连接失败
- **对话状态**：空闲、输入中、发送中、接收中

### 错误状态处理

- **网络错误**：显示重试选项
- **认证错误**：返回登录界面
- **服务器错误**：显示错误信息和重试
- **输入错误**：实时验证和提示

## 响应式设计

### 屏幕适配

- **iPhone SE (4.7")**: 紧凑布局，减少间距
- **iPhone 标准 (6.1")**: 标准布局
- **iPhone Plus/Max (6.7")**: 增加内容显示，保持比例

### 横屏适配

- **聊天界面**：保持纵向布局，不支持横屏
- **设置界面**：支持横屏，使用分栏布局
- **登录注册**：支持横屏，居中显示

### 动态字体支持

- 所有文本元素支持iOS动态字体
- 布局自适应字体大小变化
- 保持最小可点击区域44pt

这份线框图和导航流程文档提供了完整的界面结构和用户交互流程，确保开发团队能够准确理解和实现设计意图。
